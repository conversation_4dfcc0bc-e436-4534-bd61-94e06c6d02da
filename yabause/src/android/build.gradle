/*
        Copyright 2019 devMiyax(<EMAIL>)

This file is part of YabaSanshiro.

        <PERSON>baSanshiro is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2 of the License, or
(at your option) any later version.

YabaSanshiro is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

        You should have received a copy of the GNU General Public License
along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
*/

buildscript { 

    repositories {
        google()
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.0'
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.0'
        classpath "org.jlleitschuh.gradle:ktlint-gradle:9.3.0"
        classpath 'com.dicedmelon.gradle:jacoco-android:0.1.5'
    }
}




allprojects {
    apply plugin: "org.jlleitschuh.gradle.ktlint"
    repositories {
        maven {
            url 'https://maven.google.com/'
        }
    }
}
