<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2019 devMiyax(<EMAIL>)

    This file is part of YabaSanshiro.

    YabaSans<PERSON> is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    YabaSanshiro is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
-->
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">
    <PreferenceCategory android:title="@string/game_select_screen" android:key="game_select_screen" >

        <CheckBoxPreference
            android:key="pref_force_androidtv_mode"
            android:title="@string/foce_android_tv_mode"
            android:defaultValue="false" />

        <org.uoyabause.android.GameDirectoriesDialogPreference
            android:key="pref_game_directory"
            android:title="@string/select_game_directory"
            android:summary=""
            android:defaultValue="" />

        <ListPreference
            android:key="pref_install_location"
            android:title="@string/select_install_location"
            android:summary=""
            android:defaultValue="0" />
<!--
        <Preference
            android:key="select_image"
            android:title="@string/select_bk_image" />
-->

    </PreferenceCategory>

    <PreferenceCategory android:title="@string/setting_general">

        <!--
                <ListPreference
                    android:key="pref_game_download_directory"
                    android:title="@string/download_to"
                    android:dialogTitle="@string/choose_download_to"
                    android:entries="@array/entries_game_download_directory"
                    android:entryValues="@array/values_game_download_directory"
                    android:defaultValue="0" />
        -->

        <ListPreference
            android:key="pref_bios"
            android:title="@string/bios"
            android:dialogTitle="@string/choose_bios" />

        <ListPreference
            android:key="pref_cart"
            android:title="@string/cartridge"
            android:dialogTitle="@string/choose_cartridge"
            android:defaultValue="7"
            />

        <CheckBoxPreference
            android:key="pref_extend_internal_memory"
            android:title="@string/extend_internal_memory"
            android:defaultValue="true" />

        <ListPreference
            android:key="pref_cpu"
            android:title="@string/cpu_core"
            android:dialogTitle="@string/choose_cpu_core"
            android:entries="@array/entries_cpu_list_preference"
            android:entryValues="@array/entryvalues_cpu_list_preference"
            android:defaultValue="3" />

        <CheckBoxPreference
            android:key="pref_use_cpu_affinity"
            android:title="@string/use_cpu_affinity"
            android:summary="@string/perf_use_cpu_affinity_detail"
            android:defaultValue="false" />

        <CheckBoxPreference
            android:key="pref_use_sh2_cache"
            android:title="@string/use_sh2_cache"
            android:summary="@string/pref_use_sh2_cache_detail"
            android:defaultValue="true" />

        <CheckBoxPreference
            android:key="pref_auto_state_save"
            android:title="@string/pref_auto_state_save_title"
            android:summary="@string/pref_auto_state_save_detail"
            android:defaultValue="false" />

<!--
        <ListPreference
            android:key="pref_cpu_sync_per_line"
            android:title="@string/cpu_sync_per_line"
            android:dialogTitle="@string/sel_cpu_sync_per_line"
            android:entries="@array/entries_cpu_sync_per_line"
            android:entryValues="@array/entryvalues_cpu_sync_per_line"
            android:defaultValue="0" />
-->
	</PreferenceCategory>


	<PreferenceCategory android:title="@string/setting_graphics">

        <ListPreference
              android:key="pref_video"
              android:title="@string/video_core"
              android:dialogTitle="@string/choose_video_core"
              android:defaultValue="-1" />

        <CheckBoxPreference
            android:key="pref_fps"
            android:title="@string/fps"
            android:defaultValue="false" />

        <CheckBoxPreference
            android:key="pref_frameskip"
            android:title="@string/frameskip"
            android:defaultValue="true" />

        <ListPreference
            android:key="pref_frameLimit"
            android:title="@string/frameLimit"
            android:dialogTitle="@string/chooseFrameLimit"
            android:entries="@array/entriesFrameLimit"
            android:entryValues="@array/entryValuesFrameLimit"
            android:defaultValue="0" />

        <CheckBoxPreference
            android:key="pref_landscape"
            android:title="@string/lock_landscape"
            android:defaultValue="false" />

        <CheckBoxPreference
            android:key="pref_rotate_screen"
            android:title="@string/str_rotate_screen"
            android:defaultValue="false" />

        <ListPreference
            android:key="pref_filter"
            android:title="@string/video_filter"
            android:dialogTitle="@string/choose_video_filter"
            android:entries="@array/entries_video_filte_list_preference"
            android:entryValues="@array/entryvalues_video_filte_list_preference"
            android:defaultValue="0" />

        <ListPreference
            android:key="pref_polygon_generation"
            android:title="@string/polygon_generation_type"
            android:dialogTitle="@string/choose_polygon_generation_type"
            android:entries="@array/entries_polygon_generation_type"
            android:entryValues="@array/entryvalues_polygon_generation_type"
            android:defaultValue="0" />

        <ListPreference
            android:key="pref_resolution"
            android:title="@string/video_resolution"
            android:dialogTitle="@string/choose_video_resolution"
            android:entries="@array/entries_video_resolution_list_preference"
            android:entryValues="@array/entryvalues_video_resolution_list_preference"
            android:defaultValue="0" />

        <ListPreference
            android:key="pref_aspect_rate"
            android:title="@string/video_aspect_ratio"
            android:dialogTitle="@string/choose_video_aspect_ratio"
            android:entries="@array/entries_video_aspect_ratio_list_preference"
            android:entryValues="@array/entryvalues_video_aspect_ratio_list_preference"
            android:defaultValue="0" />

        <ListPreference
            android:key="pref_rbg_resolution"
            android:title="@string/rbg_resolution"
            android:dialogTitle="@string/choose_rbg_resolution"
            android:entries="@array/entries_rbg_resolution_list_preference"
            android:entryValues="@array/entryvalues_rbg_resolution_list_preference"
            android:defaultValue="0" />

        <CheckBoxPreference
            android:key="pref_use_compute_shader"
            android:title="@string/str_use_compute_shader"
            android:defaultValue="false" />

        <CheckBoxPreference
            android:key="pref_immersive_mode"
            android:title="@string/hide_status_bar_and_navigation_bar"
            android:defaultValue="false" />

     </PreferenceCategory>

    <PreferenceCategory android:title="@string/input_device_title">
        <ListPreference
              android:key="pref_player1_inputdevice"
              android:title="@string/input_device"
              android:dialogTitle="@string/choose_input_device"
              android:defaultValue="65535" />

        <org.uoyabause.android.InputSettingPreference
            android:key="pref_player1_inputdef_file"
            android:title="@string/input_device_setting"
            android:summary="" />

        <PreferenceScreen
            android:summary=""
            android:title="@string/onscrenn_pad_setting"
            android:key="on_screen_pad">
        </PreferenceScreen>


    </PreferenceCategory>

    <PreferenceCategory android:title="@string/input_device_title_player2">

        <ListPreference
              android:key="pref_player2_inputdevice"
              android:title="@string/input_device"
              android:dialogTitle="@string/choose_input_device"
              android:defaultValue="65535" />

        <org.uoyabause.android.InputSettingPreference
            android:key="pref_player2_inputdef_file"
            android:title="@string/input_device_setting"
            android:summary="" />

    </PreferenceCategory>


    <PreferenceCategory android:title="@string/setting_sound">
                <CheckBoxPreference
            android:key="pref_audio"
            android:title="@string/audio_output"
            android:defaultValue="true" />

        <ListPreference
            android:key="pref_sound_engine"
            android:title="@string/sound_engine"
            android:dialogTitle="@string/choose_sound_engine"
            android:entries="@array/entries_sound_engine_list_preference"
            android:entryValues="@array/entryvalues_sound_engine_list_preference"
            android:defaultValue="1" />

        <ListPreference
            android:key="scsp_time_sync_mode"
            android:title="@string/time_synchronization_mode"
            android:dialogTitle="@string/choose_time_synchronization_mode"
            android:entries="@array/entries_scsp_time_synchronization_mode"
            android:entryValues="@array/entryvalues_scsp_time_synchronization_mode"
            android:defaultValue="0" />

        <EditTextPreference
            android:key="pref_scsp_sync_per_frame"
            android:defaultValue="4"
            android:summary=""
            android:title="@string/scsp_sync" />

    </PreferenceCategory>

    <PreferenceCategory android:key="account_category" android:title="@string/account_category">
        <Preference
            android:key="pref_discord_link"
            android:title="@string/link_discord_account"
            android:summary="@string/summary_for_discord_login"
            android:icon="@drawable/ic_discord_logo" />

        <Preference
            android:key="pref_login_to_other"
            android:title="@string/sign_in_to_other_devices"
            android:summary="@string/summary_for_login_to_other"
            android:icon="@android:drawable/ic_menu_share" />

        <Preference
            android:key="pref_delete_account"
            android:title="@string/delete_account"
            android:summary="@string/delete_account_summary"
            android:icon="@android:drawable/ic_menu_delete" />
    </PreferenceCategory>

    <PreferenceCategory android:key="retroachievements_category" android:title="@string/retroachievements_category">
        <Preference
            android:key="pref_retroachievements_status"
            android:title="@string/retroachievements_status"
            android:summary="@string/retroachievements_not_connected"
            android:icon="@android:drawable/ic_dialog_info" />

        <Preference
            android:key="pref_retroachievements_login"
            android:title="@string/retroachievements_login"
            android:icon="@android:drawable/ic_menu_send" />

        <Preference
            android:key="pref_retroachievements_logout"
            android:title="@string/retroachievements_logout"
            android:icon="@android:drawable/ic_menu_close_clear_cancel" />

    </PreferenceCategory>

    <PreferenceCategory android:key="information" android:title="@string/information">
        <PreferenceScreen
            android:summary="https://www.yabasanshiro.com/privacy"
            android:title="@string/privacy_policy"
            android:key="on_screen_pad_x">
        </PreferenceScreen>
    </PreferenceCategory>

</PreferenceScreen>
