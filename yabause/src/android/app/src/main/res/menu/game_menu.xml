<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2019 devMiyax(<EMAIL>)

    This file is part of YabaSanshiro.

    YabaSanshiro is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    YabaSanshiro is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
-->

<menu xmlns:android="http://schemas.android.com/apk/res/android" >

    <item
        android:id="@+id/exit"
        android:icon="@drawable/ic_power_settings_new_black_24dp"
        android:title="@string/menu_exit" />

    <item android:id="@+id/button_open_cd"
        android:icon="@drawable/ic_eject_black_24dp"
        android:title="@string/open_cd_tray" />

    <item android:id="@+id/reset"
        android:icon="@drawable/ic_autorenew_black_24dp"
        android:title="@string/menu_reset" />


    <item
        android:id="@+id/menu_item_acp"
        android:icon="@drawable/ic_lock_black_24dp"
        android:title="@string/menu_acp" />


    <group
        android:id="@+id/menu_other" >


        <item
            android:id="@+id/menu_in_game_setting"
            android:icon="@drawable/baseline_settings_white_24"
            android:title="@string/setting" />
        -

        <item
            android:id="@+id/menu_item_backup"
            android:icon="@drawable/ic_save_black_24dp"
            android:title="@string/memory_manager" />
<!--
        <item
            android:id="@+id/save_state_cloud"
            android:icon="@drawable/ic_cloud_upload_black_24dp"
            android:title="@string/menu_save_state_cloud"
            android:visible="true" />

        <item
            android:id="@+id/load_state_cloud"
            android:icon="@drawable/ic_cloud_download_black_24dp"
            android:title="@string/menu_load_state_cloud"
            android:visible="true" />
-->
        -
        <item
            android:id="@+id/save_state"
            android:title="@string/menu_save_state"
            android:visible="true" />

        <item
            android:id="@+id/load_state"
            android:title="@string/menu_load_state"
            android:visible="true" />

        <item
            android:id="@+id/record"
            android:title="操作記録"
            android:visible="true" />

        <item
            android:id="@+id/play"
            android:title="操作再生"
            android:visible="true" />

    </group>
<!--
    <item android:id="@+id/gametitle"
        android:icon="@drawable/ic_screen_share_black_24dp"
        android:title="@string/menu_set_as_gametitle" />
-->

    <item android:id="@+id/report"
        android:icon="@drawable/ic_send_black_24dp"
        android:title="@string/menu_report" />

    <item
        android:id="@+id/menu_leaderboard"
        android:icon="@drawable/ic_leaderboard_black_24dp"
        android:title="@string/menu_leaderboard" />


    <item
        android:id="@+id/menu_achievements"
        android:icon="@drawable/trophy_24px"
        android:title="@string/menu_achievements" />


    <item android:title="Player1">
        <menu>
            <item
                android:id="@+id/menu_item_pad_device"
                android:icon="@drawable/ic_stat_ss_one"
                android:title="@string/onscreen_pad" />

            <item
                android:id="@+id/menu_item_pad_setting"
                android:icon="@drawable/baseline_settings_white_24"
                android:title="@string/setting" />

            <item android:id="@+id/pad_mode"
                  android:checkable="true"
                  android:icon="@drawable/ic_radio_button_checked_black_24dp"
                  android:title="@string/menu_analog" />
        </menu>
    </item>

    <item android:title="Player2">
        <menu>
            <item
                    android:id="@+id/menu_item_pad_device_p2"
                    android:icon="@drawable/ic_stat_ss_one"
                    android:title="Disconnected" />

            <item
                    android:id="@+id/menu_item_pad_setting_p2"
                    android:icon="@drawable/baseline_settings_white_24"
                    android:title="@string/setting" />

            <item android:id="@+id/pad_mode_p2"
                  android:checkable="true"
                  android:icon="@drawable/ic_radio_button_checked_black_24dp"
                  android:title="@string/menu_analog" />
        </menu>
    </item>

</menu>
