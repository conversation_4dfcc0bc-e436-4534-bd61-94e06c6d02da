<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="menu_exit">終了</string>
    <string name="menu_save_state">状態保存</string>
    <string name="menu_load_state">状態読込</string>
    <string name="load_game">ゲームを選択</string>
    <string name="setting">設定</string>
    <string name="choose_bios">BIOSの選択</string>
    <string name="choose_cartridge">カートリッジの選択</string>
    <string name="choose_cpu_core">CPUエンジンの選択</string>
    <string name="choose_video_core">描画エンジンの選択</string>
    <string name="audio_output">音声出力</string>
    <string name="fps">FPS表示</string>
    <string name="frameskip">フレームスキップ</string>
    <string name="keepaspectrate">オリジナルの画面サイズ</string>
    <string name="choose_input_device">入力デバイスの選択</string>
    <string name="input_device_title">入力デバイス1</string>
    <string name="input_device">選択</string>
    <string name="input_device_setting">キーマップの編集</string>
    <string name="onscrenn_pad_setting">オンスクリーンパッドのテスト</string>
    <string name="bios">Bios</string>
    <string name="cartridge">カートリッジ</string>
    <string name="video_core">描画エンジン</string>
    <string name="setting_sound">サウンド</string>
    <string name="setting_graphics">グラフィックス</string>
    <string name="setting_general">一般</string>
    <string name="msg_opengl_not_supported">このデバイスではOpenGL ES 3.0がサポートされていないので、ソフトウェアレンダリングしか選択できません</string>
    <string name="skip">スキップ</string>
    <string name="donate_message">
                  私たちの開発を支援してください！\n\n
		Yaba Sanshiroの上流プロジェクトYabauseの寄付は http://www.yabause.org/ で承っています。
		また、下のボタンで直接Yaba Sanshiroに寄付を送ることができます。より正確なエミュレーション,より多くの入力デバイス,より多くのプラットフォームのサポートに役立てます。\n
        寄付を行うとこの画面と広告画面は表示されなくなります。
    </string>
    <string name="thank_you">寄付をありがとうございました!</string>
    <string name="error_consume">送金に失敗しました。</string>
    <string name="do_donation">寄付する</string>
    <string name="no_thank_you">寄付しない</string>
    <string name="donate_3doller">300円</string>
    <string name="donate_5doller">500円</string>
    <string name="donate_10doller">1,000円</string>
    <string name="donate_30doller">3,000円</string>

    <string name="input_the_key">キーを入力してください</string>

    <!-- LeaderBoard strings -->
    <string name="leaderboard_title">リーダーボード</string>
    <string name="leaderboard_position">順位</string>
    <string name="leaderboard_name">名前</string>
    <string name="leaderboard_time">タイム</string>
    <string name="leaderboard_diff">差</string>
    <string name="leaderboard_no_data">このゲームにはまだリーダーボードがありません</string>
    <string name="leaderboard_not_supported">このゲームはまだリーダーボードに対応していません</string>
    <string name="menu_leaderboard">リーダーボード</string>
    <string name="joystick_is_not_connected">ゲームパッドが接続されていません</string>
    <string name="this_key_has_already_been_set">このキーはすでに設定されています</string>
	<string name="up">Up</string>
	<string name="down">Down</string>
	<string name="left">Left</string>
	<string name="right">Right</string>
	<string name="l_trigger">L Triger</string>
	<string name="r_trigger">R Trigger</string>
	<string name="start">Start</string>
	<string name="a_button">A</string>
	<string name="b_button">B</string>
	<string name="c_button">C</string>
	<string name="x_button">X</string>
	<string name="y_button">Y</string>
	<string name="z_button">Z</string>

	<string name="scale">大きさ</string>
	<string name="do_you_want_to_save_this_setting">この状態で保存しますか？</string>
	<string name="yes">はい</string>
	<string name="no">いいえ</string>
	<string name="exit">終了</string>
	<string name="ignore">無視</string>

	<string name="opengl_video_interface">OpenGL描画</string>
	<string name="software_video_interface">ソフトウェア描画</string>
	<string name="onscreen_pad">オンスクリーンパッド</string>
	<string name="select_from_other_directory">他のディレクトリから選択</string>
    <string name="video_filter">フィルタ</string>
    <string name="video_filter_fxaa">FXAA</string>
    <string name="video_filter_none">なし</string>
    <string name="choose_video_filter">ビデオフィルタの選択</string>
    <string name="cpu_core">CPUコアの選択</string>
    <string name="input_device_title_player2">入力デバイス2</string>
    <string name="invitation_cta">インストール</string>
    <string name="invitation_message">セガサターンのゲームができるアプリです</string>
    <string name="invitation_title">このアプリを紹介する</string>
    <string name="invite">紹介</string>
    <string name="invite_message">このアプリを紹介しませんか？</string>
    <string name="transparency">透明度</string>
    <string name="lock_landscape">横画面 固定</string>
    <string name="choose_polygon_generation_type">ポリゴン描画方法の選択</string>
    <string name="choose_sound_engine">サウンドエンジンの選択</string>
    <string name="choose_video_resolution">描画解像度の選択</string>
    <string name="donation">サポート</string>
    <string name="error">エラー</string>
    <string name="menu_report">レポート</string>
    <string name="menu_reset">リセット</string>
    <string name="menu_save_screenshot">スクリーンショット</string>
    <string name="select_bk_image">背景画像の選択</string>
    <string name="software_cpu_interface">インタプリタCPU(遅いけど正確)</string>
    <string name="sound_engine">サウンドエンジン</string>
    <string name="video_filter_bilinear">バイリニア</string>
    <string name="video_filter_scanline">スキャンライン</string>
    <string name="video_resolution">描画解像度</string>
    <string name="open_cd_tray">CDのふたを開ける</string>
    <string name="close_cd_tray">CDのふたを閉じる</string>
    <string name="menu_analog">アナログ有効</string>
    <string name="menu_acp">Action Replay Code</string>
    <string name="add_new_code">チートコードを追加</string>
    <string name="delete">削除</string>
    <string name="disable">無効化</string>
    <string name="enable">有効化</string>
    <string name="edit">編集</string>
    <string name="select_game_directory">ゲームディレクトリの選択</string>
    <string name="add_dir">追加</string>
    <string name="polygon_generation_type">ポリゴン生成方法</string>
    <string name="new_dynrec_cpu_interface">新ダイナミックリコンパイラ(インタプリタより速く,旧ダイナミックリコンパイラより正確)</string>
    <string name="dynrec_cpu_interface">旧ダイナミックリコンパイラ(速いけどバグあり)</string>
    <string name="poly_cpu_tess">CPUテッセレーション</string>
    <string name="poly_gpu_tess">GPUテッセレーション</string>
    <string name="res_native">ネイティブ(このデバイスの解像度で描画)</string>
    <string name="res_original">オリジナル(サターンの解像度)</string>
    <string name="ar_original">オリジナル(サターンのアスペクト比)</string>
    <string name="sound_curent">最新(クオリティは高いが遅い)</string>
    <string name="sound_legacy">旧エンジン(クオリティは低いが速い)</string>
    <string name="poly_triangle">パースペクティブコレクション</string>
    <string name="cancel">キャンセル</string>
    <string name="canceled">キャンセルされました</string>
    <string name="checking_file">ファイルをチェック中</string>
    <string name="choose_download_to">ダウンロード先</string>
    <string name="delete_confirm">削除してよろしいですか？</string>
    <string name="download_file_is_broken">ダウンロードしたファイルが壊れています</string>
    <string name="download_to">ダウンロード先</string>
    <string name="downloading">ダウンロード中</string>
    <string name="downloading_cd_rom">CD-ROMをダウンロード</string>
    <string name="external">SDカード</string>
    <string name="failed">ダウンロード失敗</string>
    <string name="finished">ダウンロード成功</string>
    <string name="internal">内部ストレージ</string>
    <string name="loading">ロード中</string>
    <string name="menu_delete">削除</string>
    <string name="menu_load">ロード</string>
    <string name="network_error">ネットワークエラー</string>
    <string name="serching_for_a_server">サーバーを探しています</string>
    <string name="server_error">サーバーでエラー</string>
    <string name="server_is_not_found">サーバーが見つかりませんでした</string>
    <string name="unable_to_process_file_for_md5">チェックサムの生成に失敗</string>
    <string name="you_need_wifi_connection">Wifi接続がありません</string>
    <string name="waiting_for_reading">読み込み待ち</string>
    <string name="free_space">空き容量</string>
    <string name="no_enough_free_space">空き容量が足りません</string>
    <string name="this_func_not_yet">この機能はまだ実装されていません</string>
    <string name="memory_manager">保存データー管理</string>
    <string name="menu_set_as_gametitle">現在の画面をタイトルに設定</string>
    <string name="refresh_db">ゲームリストの更新</string>
    <string name="have_donated">私はuoYabauseに寄付しました</string>
    <string name="send_your_order_id">uoYabauseに寄付したオーダー番号を送ることで、広告を消すことができます。オーダー番号は　http://payments.google.com　で確認できます。</string>
    <string name="order_number_not_found">オーダー番号が見つかりませんでした</string>
    <string name="order_number_used">このオーダー番号はすでに使われています</string>
    <string name="unsahre">共有をやめる</string>
    <string name="new_version_available">新しい Yaba Sanshiro がリリースされました</string>
    <string name="share">共有する</string>
    <string name="extend_internal_memory">内部メモリを8MBに拡張する</string>
    <string name="send">送信</string>
    <string name="send_warning">現在のスクリーンショットと状態を送信する\n(送信サイズは約2MByteになります)</string>
    <string name="status_report">状況報告:</string>
    <string name="issue_report">問題報告:</string>
    <string name="report_message_1">起動しない</string>
    <string name="report_message_2">イントロ/メニュー</string>
    <string name="report_message_3">プレイ可能</string>
    <string name="report_message_4">良好</string>
    <string name="report_message_5">完璧</string>

    <string name="game_report_message_1">最悪</string>
    <string name="game_report_message_2">悪い</string>
    <string name="game_report_message_3">普通</string>
    <string name="game_report_message_4">良い</string>
    <string name="game_report_message_5">優秀</string>
    <string name="siginin_message">
        サインインすることで、セーブデータとステートデータをクラウドに保存して、異なるデバイスでも共有できるようになります。\n
        しかし,これらの機能は実験的なので期待通り動作しなかったり、事前の予告なしに仕様変更されたり、終了したりすることがあります。
        ログインしなくてもFirebaseなどのライブラリから、アプリの改善に必要とされる情報が収集されます。詳しいプライバシーポリシーは https://www.yabasanshiro.com/privacy で参照できます。
    </string>
    <string name="agree">同意します</string>
    <string name="do_you_want_to_sign_in">ログインしますか？</string>
    <string name="never_ask_me_again">二度と聞かないでください</string>
    <string name="sign_in">ログイン</string>
    <string name="sign_in_cancelled">ログインがキャンセルされました</string>
    <string name="sign_out">ログアウト</string>
    <string name="stop">停止</string>
    <string name="sure_delete">本当に削除してよろしいですか？</string>
    <string name="accept">了承</string>
    <string name="activate">有効化</string>
    <string name="copy_to_cloud">クラウドにコピー</string>
    <string name="copy_to_external">外部メモリにコピー</string>
    <string name="copy_to_internal">内部メモリにコピー</string>
    <string name="decline">拒否</string>
    <string name="deleting_file">ファイルを削除します</string>
    <string name="dismiss_error">拒否</string>
    <string name="no_internet_connection">インターネットに接続していません</string>
    <string name="ok">OK</string>
    <string name="oops">しまった！</string>
    <string name="pause">Pause</string>
    <string name="play">Play</string>
    <string name="rate">評価</string>
    <string name="str_rotate_screen">90度回転させる</string>
    <string name="game_select_screen">ゲーム選択画面</string>
    <string name="foce_android_tv_mode">強制的にAndroidTV モード</string>
    <string name="are_you_sure_to_delete">本当に消してよいですか？</string>
    <string name="unknown_error">原因不明の問題</string>
    <string name="unknown_sign_in_response">謎の応答</string>
    <string name="scsp_sync">1フレーム毎にSCSPと同期する回数</string>
    <string name="need_to_accept">メディアファイルへのアクセスをを許可しないとこのアプリは使用できません。再起動して許可してください。どうやってISOイメージを読むつもりでした? wwwwwwww</string>
    <string name="cpu_sync_per_line">エミュレーションの優先事項</string>
    <string name="sel_cpu_sync_per_line"></string>
    <string name="speed">スピード</string>
    <string name="accuracy">正確さ</string>

    <string name="time_synchronization_mode">サウンド同期モード</string>
    <string name="choose_time_synchronization_mode">サウンド同期モード</string>
    <string name="cpu_time">CPU time (仮想CPUの時間に同期します。あなたのデバイスが十分早い場合、正確なサウンドが得られます。)</string>
    <string name="real_time">Real time (実世界の時間に同期します。あなたのデバイスが遅くても、音が途切れることはありません。)</string>
    <string name="already_have_been">" すでに寄付されている場合は、*******************までご連絡ください"</string>
    <string name="close">閉じる</string>
    <string name="video_aspect_ratio">画面縦横比</string>
    <string name="rbg_resolution">回転スクロール面の解像度</string>
    <string name="str_use_compute_shader">回転スクロール面の描画にComputeShaderを使用する</string>
    <string name="res_full_screen">フルスクリーン</string>
    <string name="about_app">このアプリについて</string>
    <string name="privacy_policy">プライバシーポリシー</string>
    <string name="preferences">設定</string>
    <string name="popular_header">人気のビデオ</string>
    <string name="play_pause_description">再生・停止ボタン</string>
    <string name="related_movies">関連ビデオ</string>
    <string name="res_1080p">1080p</string>
    <string name="res_16p9">16:9</string>
    <string name="res_2x">2x</string>
    <string name="res_4p3">4:3</string>
    <string name="res_4x">4x</string>
    <string name="res_720p">720p</string>
    <string name="version">Version: %1$s</string>
    <string name="show_fps">FPSを表示する</string>
    <string name="enable_frame_skip">フレームスキップを有効にする</string>
    <string name="choose_rbg_resolution">回転スクロール面の解像度</string>
    <string name="choose_video_aspect_ratio">アスペクト比の選択</string>
    <string name="welcome">
        <![CDATA[
        # ようこそ! \n\n

       以下の手順でセガサターンのゲームを実行します。\n\n

        1. CD Manipulatorなどを使ってCD-ROMのイメージ作成します。そのほかのイメージ作成ツールは[ここ](http://wiki.redump.org/index.php?title=Useful_Links)にあります。\n
        2. 作成されたCD-ROMイメージをスマホの **\"%1$s\"** にコピーします。 \n
        3. 画面左端をスワイプしてドローワーメニューを表示して \"ゲームリストの更新\" をタップします。ゲーム一覧が更新されます。\n
        4. 一覧をタップするとゲームが始まります。\n
        5. 遊ぶ!\n
        \n
        より詳しい使い方は私たちのthis [Youtube video](https://www.youtube.com/watch?v=Ch6_KhhAg10)を見てください。\n
        ]]>
    </string>

    <string name="welcome_11">
        <![CDATA[
        # ようこそ！ \n\n

        セガサターンのゲームをプレイするには4つの方法があります\n\n

        ## 1. ゲームをこのAndroidデバイスに直接コピー\n

        ISOイメージを **\"%1$s\"** にコピーします\n

        ## 2. 外部SDカードにコピー\n

        ISOイメージをSDカードの **\"%2$s\"** にコピーします\n

        ## 3. +ボタンをタップ\n

        すでにCHDまたはzip圧縮したISOイメージファイルがある場合,+ボタンをタップしてファイルを選択します。 \n\n

        ## 4. ISOイメージがあるディレクトリを指定\n

        [設定] -> [ゲームディレクトリの選択] -> [追加] でISOイメージがあるディレクトリを選択します。  \n\n

        詳細については、当社の [ウェブサイト](https://www.yabasanshiro.com/howto#android)を参照してください。\n
        ]]>
    </string>

    <string name="delete_confirm_title">削除確認</string>
    <string name="not_available">利用できません!</string>
    <string name="only_pro_version">この機能はPro versionのみ使用できます</string>
    <string name="got_it">了解!</string>
    <string name="menu_save_state_cloud">状態をクラウドに保存</string>
    <string name="menu_load_state_cloud">クラウドから状態を復帰</string>
    <string name="hide_status_bar_and_navigation_bar">ステータスバーとナビゲーションバーを隠す</string>
    <string name="sign_in_to_other_devices">ほかのデバイスにログインする</string>

    <string name="install_game_message">インストールするとこのメニュー画面から直接起動できるようになりますが、 ストレージを</string>
    <string name="install_game_message_after">MB使用します。</string>
    <string name="frameLimit">速度制限</string>
    <string name="frameLimitDouble">倍速</string>
    <string name="frameLimitNormal">等速</string>
    <string name="frameLimitUnLimited">制限なし</string>
    <string name="pro_version_available">プロバージョン\n\n * ゲームインストール数に制限なし\n * 状態保存をデバイス間で共有\n * 広告なし\n\n</string>
    <string name="remaining_installation_count">残インストール可能数</string>
    <string name="remaining_installation_count_is">残インストール可能数: </string>
    <string name="do_you_want_to_install">インストールしますか?</string>
    <string name="which_storage">どっちのストレージ？</string>
    <string name="or_place_file_to">または、ゲームを手動で \"%1$s\" に配置します</string>
    <string name="agreement">
    STARTボタンをタップすると <a href="https://www.yabasanshiro.com/terms-of-use">利用規約</a>に同意したことになります。
    注: このアプリでのデータの取り扱いについては<a href="https://www.yabasanshiro.com/privacy"> プライバシーポリシー </a> に記載されています。 アプリの改善に役立てるため、 診断データは自動的に devmiyaxおよびサードパーティーサービスに送信されます。
    </string>

    <string name="use_sh2_cache">
        SH2 cacheを使用する
    </string>

    <string name="restore_defaults">
        初期設定に戻す
    </string>
    <string name="menu_detail">詳細</string>
    <string name="last_play_game">最後にプレイしたゲーム：</string>
    <string name="device">デバイス:</string>
    <string name="auto_backup_memory_sync">自動バックアップ同期</string>
    <string name="auto_backup_sync">自動バックアップ同期</string>
    <string name="msg_fail_to_upload_backup_data_to_cloud">バックアップデータをクラウドにアップロードできませんでした</string>
    <string name="msg_fail_to_unzip_backup_data_from_cloud">バックアップデータをクラウドから展開できませんでした</string>
    <string name="msg_success_to_download_backup_data_from_cloud">バックアップデータのクラウドからダウンロードに成功しました。</string>
    <string name="msg_fail_to_download_backup_data_from_cloud">クラウドからバックアップデータをダウンロードできません</string>
    <string name="msg_fail_to_zip_backup_data">バックアップデータを圧縮できませんでした</string>
    <string name="msg_success_to_upload_backup_data_to_cloud">バックアップデータのクラウドへのアップロードに成功しました</string>

    <string name="perf_use_cpu_affinity_detail">非対称のマルチコアCPUの場合、高クロックの物が優先的に使用されます。デバイスによっては逆にパフォーマンスが落ちる場合があります</string>
    <string name="pref_use_sh2_cache_detail">SH2のキャッシュ処理をエミュレートを有効にすることで互換性を向上します。しかしエミュレーション速度が犠牲になります。</string>
    <string name="pref_auto_state_save_title">自動状態保存</string>
    <string name="pref_auto_state_save_detail">終了時に自動的に状態を保存します。次回起動時に保存したところから始められます。正常に再現できない場合があります。</string>

    <string name="auto_state_save_data_found">自動状態保存データが見つかった！</string>
    <string name="auto_state_detail">状態を読み込みますか？ 残念ながらあまり信用できません。 うまく動作しない場合は キャンセル をタップしてください</string>

    <string name="force_feedback">フォース フィードバック</string>
    <string name="visual_feedback">ビジュアル フィードバック</string>
    <string name="show_analog_dpad_switch_button">アナログ/Dpad切り替えボタンを表示</string>

    <string name="file_not_found">ファイルが見つかりません：%1$s</string>
    <string name="i_o_error_occurred">入出力エラーが発生しました：%1$s</string>
    <string name="read_permission_denied">読み取り権限が拒否されました：%1$s</string>
    <string name="other_file_error">その他のファイルエラー：%1$s</string>
    <string name="no_game_file_is_selected">ゲームファイルが選択されていません</string>
    <string name="search_games">ゲームを検索</string>
    <string name="sort_by">並び替え</string>
    <string name="sort_by_name">名前順</string>
    <string name="sort_by_date">発売日順</string>
    <string name="sort_by_recently_played">最近プレイした順</string>
    <string name="fail_to_open_with">%1$sを%2$sで開けませんでした</string>
    <string name="fail_to_open">%1$sを開けませんでした</string>
    <string name="failed_to_initialize">初期化に失敗しました</string>
    <string name="fail_to_get_game_code_this_file_is_not_sega_saturn_game">ゲームコードの取得に失敗しました。このファイルはSEGA Saturnのゲームではありません。</string>
    <string name="fail_to_initialize_emulator">エミュレータの初期化に失敗しました</string>
    <string name="notification_permission_title">通知許可のリクエスト</string>
    <string name="notification_permission_message">通知を許可すると開発者からのニュースを受け取ることができます</string>
    <string name="report_notice">
        &lt;h2&gt;レビューを投稿！&lt;/h2&gt; &lt;br&gt; 注意：あなたのレビュー内容、ユーザー名、プロフィールアイコンは公開されます。投稿する前に&lt;a href="https://www.yabasanshiro.com/privacy"&gt;プライバシーポリシー&lt;/a&gt;をご確認ください。
    </string>
    <string name="account_category">アカウント</string>

    <!-- Discord Integration Strings -->
    <string name="discord_logo">Discordロゴ</string>
    <string name="link_discord_account">Discordアカウントを連携</string>
    <string name="discord_link_prompt_title">Discordアカウントを連携する</string>
    <string name="discord_link_prompt_message">Discordアカウントを連携しますか？これにより、プロフィールがDiscordのユーザー名とアバターで更新されます。</string>
    <string name="discord_account_linked">Discordアカウントが正常に連携されました！</string>
    <string name="discord_account_link_failed">Discordアカウントの連携に失敗しました</string>
    <string name="discord_link_error">Discord連携エラー</string>
    <string name="discord_link_cancelled">Discord連携がキャンセルされました</string>
    <string name="discord_already_linked">Discordアカウントはすでに連携されています</string>
    <string name="discord_unlink_account">Discordアカウントの連携を解除</string>
    <string name="discord_unlink_confirm">Discordアカウントの連携を解除してもよろしいですか？</string>
    <string name="discord_account_unlinked">Discordアカウントの連携が解除されました</string>
    <string name="discord_link_title">Discordアカウントを連携する</string>
    <string name="discord_link_description">Discordアカウントを連携して、このアプリでDiscordのユーザー名とアバターを使用します。</string>
    <string name="skip_for_now">今はスキップ</string>
    <string name="menu_link_discord">Discordアカウントを連携</string>
    <string name="first_login_discord_prompt">Discordアカウントを連携しますか？</string>
    <string name="discord_link_prompt_yes">はい、アカウントを連携します</string>
    <string name="discord_link_prompt_no">いいえ、結構です</string>
    <string name="discord_oauth_redirect_title">Discord認証</string>
    <string name="discord_oauth_processing">Discordアカウント連携を処理中...</string>
    <string name="summary_for_discord_login">ユーザー名とアバター画像をDiscordのものと同期します。</string>
    <string name="summary_for_login_to_other">表示されるPINを使用して、Yaba SsanshiroのWindows版にログインできます。</string>
    <string name="delete_account">アカウントを削除</string>
    <string name="delete_account_summary">アカウントと関連するすべてのデータを削除します</string>
    <string name="delete_account_confirmation_title">アカウント削除</string>
    <string name="delete_account_confirmation_message">本当にアカウントを削除しますか？これによりアカウントと関連するすべてのデータが完全に削除されます。この操作は元に戻せません。</string>
    <string name="account_deleted">アカウントが正常に削除されました</string>
    <string name="account_deletion_failed">アカウントの削除に失敗しました</string>
    <string name="discord_link_not_available_on_tv">この機能はAndroid TVでは利用できません。スマートフォンまたはタブレットで操作してください。</string>

    <!-- Cloud Game Backup -->
    <string name="backup_to_cloud">クラウドにバックアップ</string>
    <string name="remove_from_cloud">クラウドから削除</string>
    <string name="restore_from_cloud">クラウドから復元</string>
    <string name="backup_success">ゲームのバックアップに成功しました</string>
    <string name="backup_failed">ゲームのバックアップに失敗しました</string>
    <string name="restore_success">ゲームの復元に成功しました</string>
    <string name="restore_failed">ゲームの復元に失敗しました</string>
    <string name="backup_limit_reached">バックアップ制限に達しました（最大1ゲーム）</string>
    <string name="legal_warning_title">法的通知</string>
    <string name="legal_warning_message">このファイルは、所有しているゲームのバックアップとしてのみ使用してください。ゲームバックアップに関するお住まいの国の法律に従ってください。</string>
    <string name="cloud_only_game">クラウドで利用可能（タップしてダウンロード）</string>
    <string name="download_from_cloud">クラウドからダウンロード</string>
    <string name="remove_success">ゲームをクラウドから削除しました</string>
    <string name="remove_failed">ゲームのクラウドからの削除に失敗しました</string>

    <!-- Backup Replacement Dialog -->
    <string name="backup_limit_dialog_title">バックアップ制限に達しました</string>
    <string name="backup_limit_dialog_message">バックアップできるゲームの最大数に達しました。置き換えるゲームを選択してください：</string>
    <string name="replace">置き換え</string>
    <string name="backup_date">%1$sにバックアップ</string>
    <string name="no_game_selected">置き換えるゲームを選択してください</string>
    <string name="replace_backup_success">バックアップの置き換えに成功しました</string>
    <string name="replace_backup_failed">バックアップの置き換えに失敗しました</string>
    
    <!-- RetroAchievements Integration Strings -->
    
    <!-- RetroAchievements Settings -->
    <string name="retroachievements_category">RetroAchievements</string>
    <string name="retroachievements_username">ユーザー名</string>
    <string name="retroachievements_password">パスワード</string>
    <string name="retroachievements_login">RetroAchievementsにログイン</string>
    <string name="retroachievements_logout">RetroAchievementsからログアウト</string>
    <string name="retroachievements_status">ステータス</string>
    <string name="retroachievements_hardcore_mode">ハードコアモード</string>
    <string name="retroachievements_hardcore_mode_summary">実績に影響するセーブステート、チートなどの機能を無効にします</string>
    <string name="retroachievements_auto_login">自動ログイン</string>
    <string name="retroachievements_auto_login_summary">アプリ起動時に自動的にログインします</string>
    <string name="retroachievements_logged_in_as">ログイン中: %s</string>
    <string name="retroachievements_not_connected">RetroAchievementsに接続していません</string>
    <string name="retroachievements_connected">RetroAchievementsに接続済み</string>
    
    <!-- Achievement List -->
    <string name="menu_achievements">実績一覧</string>
    <string name="achievements_title">実績一覧</string>
    <string name="achievements_unlocked">解除済み</string>
    <string name="achievements_locked">未解除</string>
    <string name="achievements_unsupported">未対応</string>
    <string name="achievements_progress">進行状況: %d%%</string>
    <string name="achievements_no_game_loaded">ゲームが読み込まれていません</string>
    <string name="achievements_not_logged_in">RetroAchievementsにログインしていません</string>
    
    <!-- RetroAchievements Settings Fragment -->
    <string name="retroachievements_settings_title">RetroAchievements設定</string>
    <string name="retroachievements_login_section">RetroAchievementsログイン</string>
    <string name="retroachievements_settings_section">設定</string>
    <string name="retroachievements_username_hint">ユーザー名</string>
    <string name="retroachievements_password_hint">パスワード</string>
    <string name="retroachievements_hardcore_description">実績のハードコアモードを有効にします（セーブステート、チートなどを無効化）</string>
    <string name="retroachievements_auto_login_description">アプリ起動時にRetroAchievementsに自動ログインします</string>
    <string name="retroachievements_login_failed">ログインに失敗しました: %s</string>
    <string name="retroachievements_login_success">ログインに成功しました！</string>
    <string name="retroachievements_logout_success">RetroAchievementsからログアウトしました</string>
    <string name="retroachievements_login_required">ユーザー名とパスワードを入力してください</string>
    <string name="retroachievements_logging_in">ログイン中...</string>
    
    <!-- RetroAchievements Notifications -->
    <string name="retroachievements_achievement_unlocked">🏆 実績</string>
    <string name="retroachievements_unofficial_achievement">🟠 非公式</string>
    <string name="retroachievements_leaderboard_notification">📊 リーダーボード</string>
    <string name="retroachievements_login_notification">RetroAchievementsログイン</string>
    <string name="retroachievements_login_success_message">%1$sとしてログイン%2$s</string>
    <string name="retroachievements_points_suffix"> (%dポイント)</string>
    <string name="retroachievements_game_achievements_message">%2$d個中%1$d個の実績を解除しました。</string>
    <string name="retroachievements_no_achievements_message">このゲームには実績がありません。</string>
    <string name="retroachievements_mastery_completed">👑 完了</string>
    <string name="retroachievements_mastery_hardcore">🔥 制覇</string>
    <string name="retroachievements_mastery_achievements_points">%1$d個の実績、%2$dポイント</string>
    <string name="retroachievements_mastery_playtime">プレイ時間: %s</string>
    <string name="retroachievements_server_error">⚠️ サーバーエラー</string>
    <string name="retroachievements_submit_score">送信</string>
    <string name="retroachievements_points_abbreviation">pt</string>
</resources>
