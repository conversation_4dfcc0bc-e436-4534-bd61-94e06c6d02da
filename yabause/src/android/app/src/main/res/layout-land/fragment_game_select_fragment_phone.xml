<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2019 devMiyax(<EMAIL>)

    This file is part of YabaSanshiro.

    YabaSanshiro is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    YabaSanshiro is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
-->
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout_game_select"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    tools:openDrawer="start"
    >

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/main_appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimaryDark"
            android:elevation="4dp"
            android:theme="@style/AppTheme.AppBarOverlay">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/colorPrimaryDark"
                app:layout_scrollFlags="scroll|snap"
                app:popupTheme="@style/ThemeOverlay.Material3.Light" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- Main content area with game list, game info, and boxart -->
        <LinearLayout
            android:id="@+id/main_content_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.8"
            android:orientation="vertical"
            android:padding="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="@color/colorPrimaryDark"
                    android:padding="8dp">

                    <androidx.cardview.widget.CardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/fastlane_background"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="2dp">

                        <androidx.appcompat.widget.SearchView
                            android:id="@+id/search_view"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:iconifiedByDefault="true"
                            android:queryHint="@string/search_games"
                            app:queryBackground="@android:color/transparent"
                            app:iconifiedByDefault="true"
                            app:searchIcon="@android:drawable/ic_menu_search"
                            app:closeIcon="@android:drawable/ic_menu_close_clear_cancel"
                            android:theme="@style/ThemeOverlay.AppCompat.Dark" />
                    </androidx.cardview.widget.CardView>

                    <ImageButton
                        android:id="@+id/sort_button"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="8dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/sort_by"
                        android:src="@android:drawable/ic_menu_sort_by_size" />

                </LinearLayout>


            <!-- Game list area (slimmer) -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view_games"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/default_background" />

            </LinearLayout>

            <ScrollView
                android:id="@+id/empty_message_parent"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@id/fab"
                android:isScrollContainer="false"
                android:padding="16dp"
                android:layout_marginBottom="110dp"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <TextView
                    android:id="@+id/empty_message"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/welcome" />
            </ScrollView>


            <!-- Game info area (new middle section) -->
            <LinearLayout
                android:id="@+id/game_info_section"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1.0"
                android:orientation="vertical"
                android:background="@color/default_background"
                android:padding="0dp">

                <!-- Button container with play and menu buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginTop="4dp">

                    <!-- Game version info -->
                    <TextView
                        android:id="@+id/selected_game_version"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:layout_weight="1"
                        android:textAlignment="center"
                        android:textColor="@color/colorAccent"
                        android:textSize="14sp" />


                    <!-- Play button -->
                    <ImageButton
                        android:id="@+id/play_game_button"
                        style="@style/Widget.Material3.Button.IconButton"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_gravity="center_vertical|end"
                        android:layout_marginEnd="16dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:src="@android:drawable/ic_media_play"
                        app:tint="@color/colorPrimary" />

                    <!-- Menu button -->
                    <ImageButton
                        android:id="@+id/selected_game_menu"
                        style="@style/Widget.Material3.Button.IconButton"
                        android:layout_gravity="center_vertical|end"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/game_menu"
                        app:srcCompat="@drawable/ic_more_vert_black_24dp"
                        app:tint="@color/lb_default_brand_color" />

                </LinearLayout>

                <!-- Boxart display area -->
                <FrameLayout
                    android:id="@+id/boxart_container"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="start|top"
                    android:layout_margin="12dp"
                    android:background="@color/default_background"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:forceHasOverlappingRendering="false"
                    android:paddingLeft="64dp"
                    android:paddingTop="4dp"
                    android:paddingRight="64dp"
                    android:paddingBottom="84dp">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="start|top"
                        android:minWidth="200dp"
                        android:minHeight="300dp"
                        app:cardBackgroundColor="@color/default_background"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="0dp">

                        <ImageView
                            android:id="@+id/boxart_image"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:adjustViewBounds="true"
                            android:scaleType="fitCenter"
                            android:src="@drawable/missing" />
                    </androidx.cardview.widget.CardView>

                </FrameLayout>


            </LinearLayout>

        </LinearLayout>


        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|bottom"
            android:layout_margin="16dp"
            android:background="@color/colorPrimaryDark"
            android:contentDescription="@string/add_a_game"
            android:src="@drawable/baseline_add_black_24"
            app:backgroundTint="@color/colorPrimaryDark" />



    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <FrameLayout
        android:id="@+id/ext_fragment"
        android:layout_weight="2"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />



    <include
        android:id="@+id/llProgressBar"
        android:visibility="gone"
        layout="@layout/layout_progress_bar_with_text"/>

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/lb_error_background_color_opaque"
        android:fitsSystemWindows="false"
        app:headerLayout="@layout/nav_header_main"
        app:insetForeground="#0000"
        app:menu="@menu/game_select_menu" />

</androidx.drawerlayout.widget.DrawerLayout>
