<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2019 devMiyax(<EMAIL>)

    This file is part of YabaSanshiro.

    YabaSanshiro is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    YabaSanshiro is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
-->
<resources>

    <style name="m3t" parent="Theme.Material3.Dark">
    </style>

    <style name="AppTheme" parent="Theme.Material3.Dark.NoActionBar">



        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:textColorPrimary">@color/lb_page_indicator_arrow_background</item>
        <item name="android:windowBackground">@color/default_background</item>
        <item name="colorAccent">@color/colorAccent</item>
        <!-- Material3 specific attributes -->
        <item name="colorSurfaceVariant">@color/colorPrimary</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="colorOutline">@color/colorAccent</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSurface">@color/default_background</item>
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/white</item>
    </style>

    <style name="GameScreenTheme" parent="AppTheme">
        <item name="colorControlHighlight">#00000000</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.Material3.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.Material3.Light" />

    <style name="AppTheme.Launcher">
        <item name="android:windowBackground">@drawable/launch_screen</item>
    </style>

    <style name="Toolbar.TitleText" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:maxLines">2</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="FullScreenDialogStyle" parent="Theme.Material3.Dark.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorPrimary">@color/colorPrimary</item>

        <!-- Set this to true if you want Full Screen without status bar -->
        <item name="android:windowFullscreen">false</item>

        <item name="android:windowIsFloating">false</item>

        <!-- This is important! Don't forget to set window background -->
        <item name="android:windowBackground">@color/colorPrimaryDark</item>

        <!-- Additionally if you want animations when dialog opening -->
        <!--
        <item name="android:windowEnterAnimation">@anim/slide_up</item>
        <item name="android:windowExitAnimation">@anim/slide_down</item>
        -->
    </style>

</resources>



