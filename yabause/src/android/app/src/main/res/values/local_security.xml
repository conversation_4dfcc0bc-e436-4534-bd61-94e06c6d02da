<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2019 devMiyax(<EMAIL>)

    This file is part of YabaSanshiro.

    YabaSanshiro is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    YabaSanshiro is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
-->
<resources>
    <string name="basic_user" translatable="false" >girigiri</string>
    <string name="basic_password" translatable="false" >yoyuu</string>
    <string name="ad_app_id" translatable="false" >ca-app-pub-2526043010907640~**********</string>
    <string name="banner_ad_unit_id" translatable="false" >ca-app-pub-2526043010907640/**********</string>
    <string name="banner_ad_unit_id2" translatable="false" >ca-app-pub-2526043010907640/**********</string>
    <string name="global_tracker" translatable="false" >***********-1</string>
    <string name="donation_publickey" translatable="false">MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuVRBiP7+5be0nXODtbLMnB72DYT4+gK/6VoN+9wYoI2/VSV19nO0WLxYf6nkZCZC8vYZMSc7Xzh+RQTS/ECD1JlN0aG9iLVZYWAlqkSVA5TxvU6M/io3JE8+aTDrgUFvrOKFJA2OyfjmlE2qULRsOhRKIGUp3HHa9ikE+8X7JiXyo+XlR6Q5uShuw7LSt2WvSF1SB2ABa4NZ2oybDSr81bRwPksuXXN615fYj3tZYEpW1PuvxHsSHOGQSi0D9PZiGbJZG+rbx03zy3f7akKSW2L6B338yCH88Ul5ZdxPfcdzclaldpltTuYnQpKEro3xDhCmhRcWZeaJVQuF6mxoiwIDAQAB</string>
    <string name="donation_payload" translatable="false">operation_mind_crime</string>
    <string name="check_order_url" translatable="false">https://www.uoyabause.org/</string>
    <string name="host" translatable="false">yabasanshiro</string>
    <string name="schema" translatable="false">saturngame</string>
    <string name="nend_spoid" translatable="false">967315</string>
    <string name="nend_apikey" translatable="false">eaf782c480865e3c8927fa9453b7a15a6636ad9a</string>

    <string name="key_getLoginPinIn" translatable="false" >o8mY9yWEDSHaUOlhsELMnYrdkfV3WX2o65djlJ50</string>
    <string name="key_getTokenAndDelete" translatable="false" >7t2ODcsCQG8oKZw5MVICQ2NtTl0bHL3M9fr6VDCn</string>
    <string name="url_getLoginPinIn" translatable="false" >"https://xdfvpye4sb.execute-api.us-west-2.amazonaws.com/default/getLoginPinIn"</string>
    <string name="url_getTokenAndDelete" translatable="false" >"https://5n71v2lg48.execute-api.us-west-2.amazonaws.com/default/getTokenAndDelete"</string>
    <string name="boxart_sigin" translatable="false">"Policy=****************************************************************************************************************************************************************************%26Signature=HIcjM33uLd7fNi2g-co6NHZKixMUHFlclruwVlKm~tDK1JctU69eVE0ZQ029ZIiimisNXoRhnquQloud~bqr7WyVgbRbrHMSbSQJ2f6-zSpDdXz6abNbnD6NU9MATOzfHHpct84VDMlkIoCcgBM-4fuVg-pDh5ojQMDYxh4G6zkb6xFT-YoJRD5HVltqYhZ0NEvMBs2nk8MoqytVVRuuMPYynse7ja74tF0~4yauQ3ZsDhHzAdiRZSQMNlg4GZg33GNLY1QEtelpqwWvG0JsukOtVFdVn--9hwV30ov~s7Z25ySvVGKkNTi--QS~B2fszM5hpN~RmyLkXfYOzpIkiw__%26Key-Pair-Id=K6E9XRMTU35AM"</string>

    <string name="game_id" translatable="false" >749919523054</string>

    <!-- Discord OAuth2 Configuration -->
    <string name="discord_client_id" translatable="false">637299930859438110</string>
    <string name="discord_client_secret" translatable="false">xPQ18u6gb6lfkqm7fpxUVXv42K07Glvz</string>
    <string name="discord_redirect_uri" translatable="false">yabasanshiro://discord-auth</string>
    <string name="discord_auth_url" translatable="false">https://discord.com/api/oauth2/authorize</string>
    <string name="discord_token_url" translatable="false">https://discord.com/api/oauth2/token</string>
    <string name="discord_user_url" translatable="false">https://discord.com/api/users/@me</string>
    <string name="firebase_custom_token_url" translatable="false">YOUR_BACKEND_URL_FOR_CUSTOM_TOKEN</string>

</resources>
