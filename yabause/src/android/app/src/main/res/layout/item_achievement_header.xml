<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#1A1A1A"
    android:paddingTop="16dp"
    android:paddingBottom="8dp"
    android:paddingStart="16dp"
    android:paddingEnd="16dp">

    <TextView
        android:id="@+id/header_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Category Header"
        android:textColor="#FFD700"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textAllCaps="true" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="4dp"
        android:background="#333333" />

</LinearLayout>