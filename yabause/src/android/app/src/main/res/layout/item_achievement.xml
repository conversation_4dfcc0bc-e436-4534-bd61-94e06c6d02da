<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <ImageView
        android:id="@+id/achievement_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="12dp"
        android:scaleType="centerCrop"
        android:src="@drawable/trophy_24px"
        android:contentDescription="Achievement Icon" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/achievement_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Achievement Title"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/achievement_points"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="10 pts"
                android:textColor="#FFD700"
                android:textSize="12sp"
                android:textStyle="bold"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/achievement_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Achievement description goes here"
            android:textColor="#CCCCCC"
            android:textSize="14sp"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginTop="2dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="4dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/achievement_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Locked"
                android:textSize="12sp"
                android:textStyle="italic" />

            <ProgressBar
                android:id="@+id/achievement_progress"
                android:layout_width="80dp"
                android:layout_height="16dp"
                android:layout_marginStart="8dp"
                style="?android:attr/progressBarStyleHorizontal"
                android:max="100"
                android:progress="0"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>