<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@color/halfTransparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <Spinner
        android:id="@+id/leaderboardSpinner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#CC222222"
        android:padding="8dp"
        >
        <TextView
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/leaderboard_position"
            android:textColor="#FFF"
            android:textStyle="bold" />
        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@drawable/vertical_divider" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:gravity="center"
            android:text="@string/leaderboard_name"
            android:textColor="#FFF"
            android:textStyle="bold" />
        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@drawable/vertical_divider" />

        <TextView
            android:id="@+id/timeScoreHeaderTextView"
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/leaderboard_time"
            android:textColor="#FFF"
            android:textStyle="bold" />
        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@drawable/vertical_divider" />

        <TextView
            android:layout_width="100dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/leaderboard_diff"
            android:textColor="#FFF"
            android:textStyle="bold" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/contentContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="8dp"/>

    <!-- リーダーボードがない場合の中央表示レイヤー -->
    <FrameLayout
        android:id="@+id/noLeaderboardLayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone">
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#33000000" /> <!-- 20%黒背景 -->
        <TextView
            android:id="@+id/emptyView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/leaderboard_no_data"
            android:textColor="#FFF"
            android:textSize="22sp"
            android:textStyle="bold"
            android:background="#CC222222"
            android:padding="32dp"
            android:elevation="4dp"
            android:gravity="center" />
    </FrameLayout>

        <!-- 読み込み中インジケーター -->
        <FrameLayout
            android:id="@+id/loadingLayer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center" />
        </FrameLayout>

    </FrameLayout>

    <TextView
        android:id="@+id/textViewRank"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/leaderboard_title"
        android:textColor="#FFF"
        android:textSize="18sp"
        android:layout_margin="8dp"/>
</LinearLayout>
