<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#000000">

    <!-- Hardcore Mode Setting at the top -->
    <LinearLayout
        android:id="@+id/hardcore_mode_section"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="#1a1a1a"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/retroachievements_hardcore_mode"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold" />

        <Switch
            android:id="@+id/hardcore_mode_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <!-- Hardcore Mode Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/retroachievements_hardcore_mode_summary"
        android:textColor="#AAAAAA"
        android:textSize="12sp"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:paddingBottom="16dp" />

    <!-- Achievement List Container -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/achievement_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="16dp"
            android:scrollbars="vertical"
            android:visibility="gone" />

        <TextView
            android:id="@+id/empty_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/achievements_no_game_loaded"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:gravity="center"
            android:padding="24dp" />

    </FrameLayout>

</LinearLayout>