<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2019 devMiyax(<EMAIL>)

    This file is part of YabaSanshiro.

    YabaSanshiro is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    YabaSanshiro is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
-->

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/tools"
    android:id="@+id/card_view_main"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2dp"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardBackgroundColor="@color/default_background"
    app:cardCornerRadius="4dp"
    card_view:cardCornerRadius="4dp"
    card_view:cardElevation="1dp"
    card_view:cardMaxElevation="4dp"
    card_view:cardUseCompatPadding="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="40dp"
        android:padding="16dp"
        android:tag="cards main container">

        <!-- Only game title, centered -->
        <TextView
            android:id="@+id/textViewName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:text="Game Title"
            android:textColor="@color/lb_default_brand_color"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Hidden views for compatibility with GameViewHolder -->
        <TextView
            android:id="@+id/textViewVersion"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/imageView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <ImageButton
            android:id="@+id/game_card_menu"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
