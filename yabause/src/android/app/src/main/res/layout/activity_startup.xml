<?xml version="1.0" encoding="utf-8"?>

<!--
    Copyright 2019 devMiyax(<EMAIL>)

    This file is part of YabaSanshiro.

    YabaSanshiro is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    YabaSanshiro is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
-->


<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".StartupActivity">

    <LinearLayout
        android:id="@+id/agree"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="32dp"
        android:orientation="vertical">

        <TextView

            android:id="@+id/agree_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elegantTextHeight="false"
            android:singleLine="false"
            android:text="@string/agreement"
            android:textColor="@color/black"
            android:textColorHighlight="@color/cardview_initial_background"
            android:textColorLink="@color/black" />

        <Button
            android:id="@+id/agree_and_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:layout_gravity="bottom|center_horizontal"
            android:text="START" />

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>