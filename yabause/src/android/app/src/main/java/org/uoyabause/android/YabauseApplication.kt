/*  Copyright 2019 devMiyax(<EMAIL>)

    This file is part of YabaSanshiro.

    YabaSans<PERSON> is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    <PERSON><PERSON>Sans<PERSON> is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
*/
package org.uoyabause.android

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.net.Uri
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import androidx.multidex.MultiDex
import androidx.multidex.MultiDexApplication
import com.google.android.gms.analytics.GoogleAnalytics
import com.google.android.gms.analytics.Tracker
import com.google.firebase.FirebaseApp
import org.devmiyax.yabasanshiro.BuildConfig
import org.devmiyax.yabasanshiro.R
import org.uoyabause.android.cheat.Cheat
import org.uoyabause.android.auth.RetroAchievementsAuthManager

class YabauseApplication : MultiDexApplication() {
    private var mTracker: Tracker? = null
    val TAG = "YabauseApplication"
    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    override fun onCreate() {
        super.onCreate()
        appContext = applicationContext

        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)

        GameInfo.initSigin(appContext)

        FirebaseApp.initializeApp(applicationContext)
        
        // Initialize RetroAchievements authentication at app startup
        // This will handle auto-login if enabled, without showing notifications
        initializeRetroAchievements()

        // Log.d(TAG,"Firebase token: " + FirebaseInstanceId.getInstance().getToken() );
    }
    
    /**
     * Initialize RetroAchievements authentication at application startup
     * This performs early initialization and auto-login without notifications
     */
    private fun initializeRetroAchievements() {
        try {
            Log.d(TAG, "Initializing RetroAchievements at application startup...")
            
            // Get authentication manager instance (creates if needed)
            val authManager = RetroAchievementsAuthManager.getInstance(appContext)
            
            // Initialize authentication manager which will handle auto-login if enabled
            // Note: This initialization is lightweight and doesn't show login notifications
            authManager.initialize()
            
            Log.d(TAG, "RetroAchievements authentication initialized at application level")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize RetroAchievements at application startup", e)
        }
    } // To enable debug logging use: adb shell setprop log.tag.GAv4 DEBUG

    /**
     * Gets the default [Tracker] for this [Application].
     * @return tracker
     */
    @get:Synchronized
    val defaultTracker: Tracker?
        get() {
            if (mTracker == null) {
                val analytics = GoogleAnalytics.getInstance(this)
                // To enable debug logging use: adb shell setprop log.tag.GAv4 DEBUG
                mTracker = analytics.newTracker(R.xml.global_tracker)
                mTracker!!.enableAdvertisingIdCollection(true)
            }
            return mTracker
        }

    companion object {
        lateinit var appContext: Context
            private set

        fun isPro(): Boolean {
            val prefs: SharedPreferences? = appContext.getSharedPreferences("private",
                Context.MODE_PRIVATE)
            var hasDonated = false
            if (prefs != null) {
                hasDonated = prefs.getBoolean("donated", false)
            }
            if (BuildConfig.BUILD_TYPE == "pro" || hasDonated) {
                return true
            }
            return false
        }

        fun checkDonated(ctx: Context, additionalMessage: String = ""): Int {
            // if (BuildConfig.BUILD_TYPE == "debug") {
            //    return 0
            // }
            var rtn = -1
            if (BuildConfig.BUILD_TYPE != "pro" && BuildConfig.BUILD_TYPE != "debug") {
                val prefs = ctx.getSharedPreferences("private", MODE_PRIVATE)
                val hasDonated = prefs.getBoolean("donated", false)
                if (hasDonated == false) {
                    val builder = AlertDialog.Builder(ctx)
                    builder.setTitle(R.string.not_available)
                    builder.setMessage(ctx.getString(R.string.only_pro_version) + " \n" + additionalMessage)
                    builder.setPositiveButton(R.string.got_it
                    ) { _, _ ->
                        val url =
                            "https://play.google.com/store/apps/details?id=org.devmiyax.yabasanshioro2.pro"
                        val intent = Intent(Intent.ACTION_VIEW)
                        intent.data = Uri.parse(url)
                        intent.setPackage("com.android.vending")
                        ctx.startActivity(intent)
                        rtn = -1
                    }
                    builder.setNegativeButton(R.string.cancel
                    ) { _, _ ->
                        rtn = -2
                    }

                    builder.create().show()

                    return rtn
                }
            }
            return 0
        }

        fun getVersionName(): String? {
            val pm = appContext.packageManager
            var versionName = ""
            try {
                val packageInfo = pm.getPackageInfo(appContext.packageName, 0)
                versionName = packageInfo.versionName ?: ""
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
            }
            return versionName
        }

        fun getVersionCode(): Int {
            val pm = appContext.packageManager
            var versionCode = 0
            try {
                val packageInfo = pm.getPackageInfo(appContext.packageName, 0)
                versionCode = packageInfo.versionCode
            } catch (e: PackageManager.NameNotFoundException) {
                e.printStackTrace()
            }
            return versionCode
        }
    }
}
