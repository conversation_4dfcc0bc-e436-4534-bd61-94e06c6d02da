/*
        Copyright 2019 devMiyax(<EMAIL>)

This file is part of YabaSanshiro.

        <PERSON>baSans<PERSON> is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2 of the License, or
(at your option) any later version.

<PERSON><PERSON>Sans<PERSON> is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

        You should have received a copy of the GNU General Public License
along with <PERSON><PERSON><PERSON><PERSON><PERSON>; if not, write to the Free Software
Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301  USA
*/


plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("com.google.firebase.crashlytics")
    id 'org.jetbrains.kotlin.kapt'
    //id("com.dicedmelon.gradle.jacoco-android")
}


android {
    testOptions {
        unitTests.returnDefaultValues = true
    }
    namespace "org.devmiyax.ya<PERSON>anshiro"
    signingConfigs {
        pro {
            if( System.getenv('KEY_FILE_NAME') == null ){
                storeFile file("dmy")
            }else {
                storeFile file(System.getenv('KEY_FILE_NAME'))
            }
            storePassword System.getenv('STORE_PASSWORD')
            keyAlias System.getenv('KEY_ALIAS')
            keyPassword System.getenv('KEY_PASSWORD')
        }

        release {
            if( System.getenv('KEY_FILE_NAME') == null ){
                storeFile file("dmy")
            }else {
                storeFile file(System.getenv('KEY_FILE_NAME'))
            }
            storePassword System.getenv('STORE_PASSWORD')
            keyAlias System.getenv('KEY_ALIAS')
            keyPassword System.getenv('KEY_PASSWORD')
        }

    }

    sourceSets {
        test.java.srcDirs += 'src/test/java'
        androidTest.java.srcDirs += 'src/androidTest/java'
        androidTest.assets.srcDirs += 'src/androidTest'
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
    }

    compileSdkVersion 35

    packaging {
        resources {
            excludes += ["META-INF/LICENSE.md", "META-INF/LICENSE-notice.md"]
        }
    }

    defaultConfig {
        applicationId "org.devmiyax.yabasanshioro2"
        minSdkVersion 24
        targetSdkVersion 35
        multiDexEnabled true
        versionCode 237
        versionName '1.18.1'
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
        signingConfig signingConfigs.pro
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        javaCompileOptions {
            annotationProcessorOptions {
                arguments += ["room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }

/*
        externalNativeBuild {

            // For ndk-build, instead use ndkBuild {}
            cmake {

                //cd  Passes optional arguments to CMake.
                arguments "-DANDROID_ABI=armeabi-v7a","-DANDROID_NATIVE_API_LEVEL=19","-DYAB_PORTS=android",
                        "-DYAB_WANT_C68K=FALSE","-DYAB_WANT_ARM7=TRUE","-DYAB_WANT_DYNAREC_DEVMIYAX=TRUE",
                        "-DANDROID_FORCE_ARM_BUILD=FALSE","-DCMAKE_BUILD_TYPE=Release"
            }
        }
        ndk {
            // Specifies the ABI configurations of your native
            // libraries Gradle should build and package with your APK.
            abiFilters 'armeabi-v7a'
        }
*/
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            applicationVariants.all { variant ->
                renameAPK(variant, defaultConfig, 'R')
            }
            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                unstrippedNativeLibsDir  'src/main/jniLibs'
            }
            ndk{
                debugSymbolLevel 'FULL'
            }
            resValue "string", "app_name", "Yaba Sanshiro 2"
        }
        pro {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            applicationIdSuffix ".pro"
            versionNameSuffix '-PRO'
            buildConfigField 'String', 'AUTHORITY', '"org.devmiyax.yabasanshioro2.pro.contentprovider"'
            resValue "string", "app_name", "Yaba Sanshiro 2 Pro"
            applicationVariants.all { variant ->
                renameAPK(variant, defaultConfig, 'P')
            }
            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                unstrippedNativeLibsDir  'src/main/jniLibs'
            }
            ndk{
                debugSymbolLevel 'FULL'
            }
        }
        debug {
            debuggable true
            testCoverageEnabled true
            applicationIdSuffix ".debug"
            versionNameSuffix '-DEBUG'
            resValue "string", "app_name", "Yaba Sanshiro 2 debug"
            buildConfigField 'String', 'AUTHORITY', '"org.devmiyax.yabasanshioro2.debug.contentprovider"'
            packagingOptions {
                doNotStrip "**/*/*.so"
            }
            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                //strippedNativeLibsDir  'src/main/jniLibs'
                //unstrippedNativeLibsDir  'src/main/jniLibs'
            }

        }
    }

    buildFeatures {
        viewBinding true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = '17'
    }
    buildToolsVersion '35.0.0'
    packagingOptions {
        jniLibs {
            useLegacyPackaging true
        }
    }
    lint {
        abortOnError false
    }

/*
    externalNativeBuild {
        cmake {
            path '../../../CMakeLists.txt'
        }
    }
*/
}

//crashlytics {
//    enableNdk true
//    androidNdkOut 'src/main/jniLibs'
//    androidNdkLibsOut 'src/main/jniLibs'
//    manifestPath 'src/main/AndroidManifest.xml'
//}

def renameAPK(variant, defaultConfig, buildType) {
    variant.outputs.all {
        def formattedDate = new Date().format('yyMMdd')
        outputFileName = "${applicationName}-V${variant.versionCode}-${variant.versionName}-${formattedDate}-${variant.name}.apk"
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        mavenCentral()
        maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
    }
}

task printVersionName {
    doLast {
        println android.defaultConfig.versionName
    }
}

//repositories {
//    mavenCentral()
//    maven { url "https://oss.sonatype.org/content/repositories/snapshots/" }
//    maven { url 'http://fan-adn.github.io/nendSDK-Android-lib/library' }
//    maven { url 'https://maven.fabric.io/public' }
//}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    //noinspection GradleCompatible
    implementation platform('com.google.firebase:firebase-bom:33.1.2')

    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.documentfile:documentfile:1.1.0'
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'androidx.activity:activity-ktx:1.10.1'
    implementation 'androidx.leanback:leanback:1.2.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.tvprovider:tvprovider:1.1.0'
    implementation "androidx.window:window:1.4.0"
    implementation("androidx.viewpager2:viewpager2:1.1.0")

    // RetroAchievements dependencies
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
    implementation("com.squareup.okhttp3:okhttp:4.12.0")

    implementation 'com.google.firebase:firebase-core:21.1.1'
    implementation 'com.google.firebase:firebase-auth:23.0.0'
    implementation 'com.google.firebase:firebase-database:21.0.0'
    implementation 'com.google.firebase:firebase-storage:21.0.1'
    implementation 'com.google.firebase:firebase-ads:23.6.0'
    implementation 'com.google.firebase:firebase-messaging:24.1.0'
    implementation 'com.google.firebase:firebase-config:22.1.0'
    implementation 'com.google.android.gms:play-services-analytics:18.1.1'
    implementation 'com.firebaseui:firebase-ui-auth:7.2.0'



    implementation 'com.github.bumptech.glide:glide:4.11.0'
    implementation 'commons-io:commons-io:2.13.0'
    implementation 'org.jbundle.util.osgi.wrapped:org.jbundle.util.osgi.wrapped.org.apache.http.client:4.1.2'
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ndk'
    //implementation 'net.cattaka:adapter-toolbox:0.5.2'
    implementation "com.google.android.material:material:1.12.0"
    implementation 'com.google.android.play:review-ktx:2.0.1'

    // Add the In-App Messaging dependency:
    implementation 'com.google.firebase:firebase-inappmessaging-display:21.0.0'

    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.6'

    implementation "androidx.preference:preference-ktx:1.2.1"

    implementation "io.noties.markwon:core:4.1.0"
    implementation 'com.google.firebase:firebase-firestore-ktx:25.1.2'

    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.json:json:20180813'
    testImplementation 'org.mockito:mockito-inline:2.27.0'
    testImplementation "org.hamcrest:hamcrest-all:1.3"
    testImplementation "androidx.test.ext:junit-ktx:1.2.1"
    testImplementation "androidx.test:core-ktx:1.6.1"
    testImplementation "org.robolectric:robolectric:4.3.1"
    // MockK for unit test
    testImplementation "io.mockk:mockk:1.13.10"
    // MockK for Android instrumented test
    androidTestImplementation "io.mockk:mockk-android:1.13.10"
    testImplementation "androidx.arch.core:core-testing:2.2.0"

    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    testImplementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3"
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test:rules:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'



    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"

    implementation "com.google.android.gms:play-services-games:23.2.0"

    implementation "org.apache.commons:commons-compress:1.21"
    implementation "org.tukaani:xz:1.8"

    implementation "com.android.billingclient:billing-ktx:7.0.0"

    implementation 'com.frybits.harmony:harmony:1.1.11'

    implementation 'androidx.cardview:cardview:1.0.0'

    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.8.4'
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.8.4"

    def room_version = "2.6.1"
    implementation("androidx.room:room-runtime:$room_version")
    annotationProcessor("androidx.room:room-compiler:$room_version")
    kapt "androidx.room:room-compiler:$room_version"

    implementation 'jp.wasabeef:glide-transformations:4.3.0'
    implementation 'jp.co.cyberagent.android:gpuimage:2.1.0'

}

// ADD THIS AT THE BOTTOM
apply plugin: 'com.google.gms.google-services'
apply plugin: 'kotlin-android'
