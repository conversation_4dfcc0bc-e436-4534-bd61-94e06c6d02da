-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception
-keep class com.crashlytics.** { *; }
-dontwarn com.crashlytics.**
-keep class com.activeandroid.** { *; }
-keep class com.activeandroid.**.** { *; }
-keep class * extends com.activeandroid.Model
-keep class * extends com.activeandroid.serializer.TypeSerializer
-keep public class org.uoyabause.android.YabauseRunnable.** { *; }
-keep class org.uoyabause.android.Yabause.** { *; }
-keepclassmembers class **.Yabause { *; }
-keep class org.uoyabause.android.achievements.RetroAchievementsManager { *; }
-keep class org.uoyabause.android.achievements.RetroAchievementsManager$* { *; }
-keepclassmembers class org.uoyabause.android.achievements.RetroAchievementsManager { *; }

# Explicitly keep JNI callback methods to prevent ProGuard from removing them
-keepclassmembers class org.uoyabause.android.achievements.RetroAchievementsManager {
    public void onAchievementUnlocked(int, java.lang.String, java.lang.String, int, java.lang.String, boolean);
    public void onLeaderboardSubmit(int, java.lang.String, java.lang.String, int);
    public void onRichPresenceUpdate(java.lang.String);
    public void onGameLoadComplete(boolean, java.lang.String);
    public void onLoginComplete(boolean, java.lang.String);
    public void onHttpRequest(java.lang.String, java.lang.String, long);
    public void onLeaderboardTrackerShow(int, java.lang.String);
    public void onLeaderboardTrackerHide(int);
    public void onLeaderboardTrackerUpdate(int, java.lang.String);
    public void onChallengeIndicatorShow(int, java.lang.String, java.lang.String);
    public void onChallengeIndicatorHide(int);
    public void onProgressIndicatorShow();
    public void onProgressIndicatorHide();
    public void onProgressIndicatorUpdate(int, java.lang.String, java.lang.String, java.lang.String, int);
    public void onGamePlacard(java.lang.String, java.lang.String, int, int);
}

-keep class org.uoyabause.android.achievements.RetroAchievementsNotification { *; }
-keep class org.uoyabause.android.auth.RetroAchievementsAuthManager { *; }
-keep class org.uoyabause.android.achievements.AchievementListFragment { *; }
-keep class org.uoyabause.android.achievements.AchievementListFragment$* { *; }
-keep class com.google.protobuf.** { *; }
-dontwarn com.google.protobuf.**

 # Add this global rule
 -keepattributes Signature

 # This rule will properly ProGuard all the model classes in
 # the package com.yourcompany.models. Modify to fit the structure
 # of your app.
 -keepclassmembers class org.uoyabause.android.backup.BackupItem {
      *;
 }

-keepclassmembers class org.uoyabause.android.cheat.CheatItem {
       *;
  }

# Android Gradle plugin generated rules
-dontwarn android.media.LoudnessCodecController$OnLoudnessCodecUpdateListener
-dontwarn android.media.LoudnessCodecController
