# サブスクリプション設定ガイド

YabaSnashiro Liteアプリでサブスクリプション機能を有効にするために必要な設定手順です。

## App Store Connect設定

### 1. サブスクリプション商品の作成

App Store Connectで以下のサブスクリプション商品を作成してください：

#### 月額サブスクリプション
- **商品ID**: `yabasanshiro_monthly_premium`
- **タイプ**: 自動更新サブスクリプション
- **サブスクリプショングループ**: Premium Features
- **期間**: 1ヶ月
- **価格**: 適切な価格を設定

#### 年額サブスクリプション
- **商品ID**: `yabasanshiro_yearly_premium`
- **タイプ**: 自動更新サブスクリプション
- **サブスクリプショングループ**: Premium Features
- **期間**: 1年
- **価格**: 適切な価格を設定（通常は月額×10-12ヶ月程度）

### 2. サブスクリプション商品の詳細設定

各商品について以下を設定：

- **表示名**: 
  - 月額: "Yaba Sanshiro 2 Premium Monthly"
  - 年額: "Yaba Sanshiro 2 Premium Yearly"

- **説明**: 
  - 無制限のゲームインストール
  - 広告なし
  - クラウドセーブ同期
  - 優先カスタマーサポート

### 3. レビュー用メタデータ

- スクリーンショット: サブスクリプション機能を示すスクリーンショット
- レビューノート: サブスクリプション機能の動作説明

## コード内の商品ID

現在のコードでは以下の商品IDが使用されています：

```swift
static let monthlyProductId = "yabasanshiro_monthly_premium"
static let yearlyProductId = "yabasanshiro_yearly_premium"
```

商品IDを変更する場合は、`SubscriptionManager.swift`ファイルの該当部分を更新してください。

## テスト手順

### 1. Sandbox環境でのテスト

1. App Store Connectでサンドボックステスターアカウントを作成
2. デバイスの設定でサンドボックスアカウントでサインイン
3. アプリをビルドしてテストデバイスにインストール
4. サブスクリプション購入フローをテスト

### 2. 確認項目

- [ ] サブスクリプション購入画面が正常に表示される
- [ ] 月額・年額プランの価格が正しく表示される
- [ ] 購入フローが正常に動作する
- [ ] 購入後にゲーム制限が解除される
- [ ] 購入後に広告が非表示になる
- [ ] リストア機能が正常に動作する
- [ ] サブスクリプション管理画面へのリンクが動作する

## 機能仕様

### サブスクリプション有効時の変更点

1. **ゲーム制限の解除**
   - Lite版の3ゲーム制限を無制限に変更
   - `FileSelectController.checkLimitation()`で制御

2. **広告の非表示**
   - バナー広告を非表示
   - `FileSelectController.viewDidLoad()`で制御

3. **UI表示の変更**
   - メイン画面のアップグレードボタンが"Premium"に変更
   - ボタンの色がオレンジから緑に変更

### エラーハンドリング

- ネットワークエラー時の処理
- 購入失敗時のアラート表示
- レシート検証失敗時のフォールバック

## リリース前チェックリスト

- [ ] App Store Connectでサブスクリプション商品を作成
- [ ] 商品IDがコードと一致することを確認
- [ ] Sandboxでの動作テスト完了
- [ ] プロダクションビルドでの最終テスト
- [ ] App Store審査ガイドラインの確認
- [ ] 利用規約・プライバシーポリシーの更新

## 注意事項

- iOS 15以上では新しいStoreKit 2 APIを使用
- iOS 14以下では従来のStoreKit APIを使用
- サブスクリプション状態の同期に時間がかかる場合があります
- レシート検証はクライアントサイドの簡易実装です（プロダクションではサーバーサイド検証を推奨）