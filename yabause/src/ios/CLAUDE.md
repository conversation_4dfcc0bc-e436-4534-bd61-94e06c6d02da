# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

YabaSnashiro is a Sega Saturn emulator for iOS with two app variants: the full version (YabaSnashiro) and a lite version (<PERSON><PERSON><PERSON>nashiro-Lite) with ads. The project combines a C/C++ core emulator (built via CMake) with a Swift iOS frontend using extensive Firebase integration for user management, leaderboards, and cloud saves.

## Build System

### Core Emulator Build (CMake)
```bash
mkdir build
cd build
cmake ../yabause -DCMAKE_TOOLCHAIN_FILE=../yabause/src/ios/ios.toolchain.cmake -DPLATFORM=OS64 -DYAB_PORTS=ios -DYAB_WANT_C68K=FALSE -DYAB_WANT_SH2_CACHE=TRUE -DSH2_DYNAREC=FALSE -DYAB_WANT_DYNAREC_DEVMIYAX=FALSE
make
```

For iOS Simulator:
```bash
cmake ../yabause -DCMAKE_TOOLCHAIN_FILE=../yabause/src/ios/ios.toolchain.cmake -DPLATFORM=SIMULATORARM64 -DYAB_PORTS=ios -DYAB_WANT_C68K=FALSE -DYAB_WANT_SH2_CACHE=TRUE -DSH2_DYNAREC=FALSE
```

### iOS App Build (Fastlane)
Navigate to `uoyabause/` directory first:

**Development builds:**
```bash
bundle exec fastlane build          # Full version
bundle exec fastlane build_lite    # Lite version
```

**TestFlight/Release builds:**
```bash
bundle exec fastlane beta           # Full version to TestFlight
bundle exec fastlane beta_lite      # Lite version to TestFlight
```

**Screenshots:**
```bash
bundle exec fastlane screenshots_all   # Generate localized screenshots for both versions
```

### Dependencies
```bash
# Install Ruby dependencies
bundle install

# Install CocoaPods dependencies
pod install
```

## Project Structure

- **Core Emulator**: C/C++ Saturn emulator code lives in parent `yabause/` directory
- **iOS Port**: `src/ios/` contains iOS-specific emulator bridge code
- **iOS App**: `uoyabause/uoyabause/` contains the Swift iOS application
- **Xcode Projects**: `uoyabause/YabaSnashiro.xcworkspace` (use workspace, not .xcodeproj)

### Key iOS App Components

**Architecture Pattern**: Standard iOS MVC with Firebase backend integration

**Main Controllers:**
- `MainScreenController.swift` - Home screen with game library
- `GameViewController.swift` - In-game emulator interface  
- `GameMainViewController.swift` - Game metadata and launch logic
- `SettingsViewController.swift` - App configuration
- `LoginViewController.swift` - Firebase authentication

**Firebase Integration:**
- Authentication (Google Sign-In, anonymous)
- Firestore (user profiles, game metadata)
- Realtime Database (leaderboards)
- Storage (cloud saves, game covers)
- Remote Config (feature flags)
- Crashlytics (error reporting)

**Game-Specific Features:**
- `game/BaseGame.swift` - Abstract game interface
- `game/SegaRally.swift` - Sega Rally specific features
- `game/SonicR.swift` - Sonic R specific features
- Cheat system with cloud sync in `Cheat/` directory

**Input System:**
- `KeyMapper.swift` - Controller input mapping
- `PadButton.swift` - Virtual gamepad implementation
- `SaturnKey.swift` - Saturn controller key definitions

## Testing

The project includes XCTest suites:
- `uoyabauseTests/` - Unit tests
- `uoyabauseUITests/` - UI automation tests
- Game-specific tests for Sega Rally in test files

Run tests through Xcode or:
```bash
xcodebuild test -workspace YabaSnashiro.xcworkspace -scheme YabaSnashiro -destination 'platform=iOS Simulator,name=iPhone 15'
```

## App Variants

**YabaSnashiro (Full)**: Complete emulator experience
**YabaSnashiro-Lite**: Same features + Google Mobile Ads integration

Both variants share the same codebase with build-time configuration differences handled through Xcode schemes and Info.plist variants.

## Development Notes

- Always use `YabaSnashiro.xcworkspace`, never the `.xcodeproj` file directly
- Firebase configuration files are environment-specific (Debug/Release variants)
- The emulator core must be built via CMake before iOS app compilation
- MetalANGLE framework provides OpenGL ES compatibility
- Supports iOS 14.0+ minimum deployment target